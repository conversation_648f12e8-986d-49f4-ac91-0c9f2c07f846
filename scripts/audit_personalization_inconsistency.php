<?php

require_once __DIR__ . '/../vendor/autoload.php';

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PersonalizationAudit
{
    public function run()
    {
        echo "=== Personalization Data Inconsistency Audit ===\n";
        echo "Analyzing orders from the last 2 months...\n\n";
        
        $sixMonthsAgo = now()->subMonths(2);
        
        // Get all orders from last 6 months with products
        $orders = DB::table('orders')
            ->where('created_at', '>=', $sixMonthsAgo)
            ->whereJsonLength('products', '>', 0)
            ->select(['id', 'created_at', 'products', 'status'])
            ->get();
        
        $affectedOrders = [];
        $affectedProducts = [];
        $totalInconsistentProducts = 0;
        $datePatterns = [];
        $productTypePatterns = [];
        
        foreach ($orders as $order) {
            $products = json_decode($order->products, true);
            $orderHasIssue = false;
            
            foreach ($products as $index => $product) {
                // Check if product has personalization but missing personal
                if ($this->hasPersonalizationIssue($product)) {
                    $orderHasIssue = true;
                    $totalInconsistentProducts++;
                    
                    // Track affected products
                    $affectedProducts[] = [
                        'order_id' => $order->id,
                        'product_index' => $index,
                        'product_id' => $product['product_id'] ?? null,
                        'title' => $product['title'] ?? 'Unknown',
                        'sku' => $product['sku'] ?? null,
                        'item_type' => $product['item_type'] ?? null,
                        'has_personalization' => isset($product['personalization']),
                        'has_personal' => isset($product['personal']),
                        'personalizes_exists' => isset($product['model']['personalizes']),
                        'created_at' => $order->created_at
                    ];
                    
                    // Track date patterns
                    $dateKey = Carbon::parse($order->created_at)->format('Y-m');
                    $datePatterns[$dateKey] = ($datePatterns[$dateKey] ?? 0) + 1;
                    
                    // Track product type patterns
                    $itemType = $product['item_type'] ?? 'unknown';
                    $productTypePatterns[$itemType] = ($productTypePatterns[$itemType] ?? 0) + 1;
                }
            }
            
            if ($orderHasIssue) {
                $affectedOrders[] = [
                    'id' => $order->id,
                    'created_at' => $order->created_at,
                    'status' => $order->status,
                    'product_count' => count($products)
                ];
            }
        }
        
        $this->printSummary($affectedOrders, $affectedProducts, $totalInconsistentProducts, $datePatterns, $productTypePatterns, $sixMonthsAgo);
        $this->generateDetailedReport($affectedProducts);
        $this->suggestMigrationScript($affectedOrders);
    }
    
    private function hasPersonalizationIssue($product)
    {
        // Check if product has personalization data but missing personal data
        $hasPersonalization = isset($product['personalization']) && !empty($product['personalization']);
        $hasPersonal = isset($product['personal']) && !empty($product['personal']);
        $supportsPersonalization = isset($product['model']['personalizes']) && !empty($product['model']['personalizes']);
        
        return $hasPersonalization && !$hasPersonal && $supportsPersonalization;
    }
    
    private function printSummary($affectedOrders, $affectedProducts, $totalInconsistentProducts, $datePatterns, $productTypePatterns, $sixMonthsAgo)
    {
        echo "=== AUDIT SUMMARY ===\n";
        echo "Analysis Period: " . $sixMonthsAgo->format('Y-m-d') . " to " . Carbon::now()->format('Y-m-d') . "\n";
        echo "Total Affected Orders: " . count($affectedOrders) . "\n";
        echo "Total Inconsistent Products: " . $totalInconsistentProducts . "\n\n";
        
        echo "=== DATE PATTERNS ===\n";
        ksort($datePatterns);
        foreach ($datePatterns as $month => $count) {
            echo "$month: $count products affected\n";
        }
        
        if (!empty($datePatterns)) {
            $firstMonth = array_keys($datePatterns)[0];
            echo "\nIssue first appeared: $firstMonth\n";
        }
        
        echo "\n=== PRODUCT TYPE PATTERNS ===\n";
        arsort($productTypePatterns);
        foreach ($productTypePatterns as $type => $count) {
            echo "$type: $count products affected\n";
        }
        
        echo "\n=== SEVERITY ASSESSMENT ===\n";
        if (count($affectedOrders) > 50) {
            echo "🔴 HIGH PRIORITY: " . count($affectedOrders) . " orders affected - Migration script recommended\n";
        } elseif (count($affectedOrders) > 10) {
            echo "🟡 MEDIUM PRIORITY: " . count($affectedOrders) . " orders affected - Consider migration script\n";
        } else {
            echo "🟢 LOW PRIORITY: " . count($affectedOrders) . " orders affected - Code fix may be sufficient\n";
        }
    }
    
    private function generateDetailedReport($affectedProducts)
    {
        echo "\n=== DETAILED PRODUCT ANALYSIS ===\n";
        
        // Group by product ID to find patterns
        $productGroups = [];
        foreach ($affectedProducts as $product) {
            $productId = $product['product_id'] ?? 'unknown';
            $productGroups[$productId][] = $product;
        }
        
        echo "Products with multiple affected orders:\n";
        foreach ($productGroups as $productId => $products) {
            if (count($products) > 1) {
                $firstProduct = $products[0];
                echo "- Product ID: $productId ({$firstProduct['title']}) - " . count($products) . " affected orders\n";
            }
        }
        
        // Sample of affected orders for manual verification
        echo "\nSample affected orders for manual verification:\n";
        $sampleOrders = array_slice($affectedProducts, 0, 5);
        foreach ($sampleOrders as $product) {
            echo "- Order #{$product['order_id']}: {$product['title']} (SKU: {$product['sku']})\n";
        }
    }
    
    private function suggestMigrationScript($affectedOrders)
    {
        if (empty($affectedOrders)) {
            echo "\n✅ No migration needed - no affected orders found.\n";
            return;
        }
        
        echo "\n=== MIGRATION SCRIPT SUGGESTION ===\n";
        echo "Based on the audit results, here's a suggested migration approach:\n\n";
        
        $orderIds = array_column($affectedOrders, 'id');
        $orderIdsList = implode(',', array_slice($orderIds, 0, 10));
        
        echo "1. Test migration on sample orders: $orderIdsList\n";
        echo "2. Run full migration on all " . count($affectedOrders) . " affected orders\n";
        echo "3. Verify Nova admin display after migration\n\n";
        
        echo "Migration command suggestion:\n";
        echo "php artisan make:command FixPersonalizationData\n";
    }
}

// Run the audit
$audit = new PersonalizationAudit();
$audit->run();