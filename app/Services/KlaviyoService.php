<?php

namespace App\Services;

use App\Category;
use App\Order;
use App\DTO\Klaviyo\BulkCatalogJobDTO;
use App\DTO\Klaviyo\CatalogItemDTO;
use App\DTO\Klaviyo\CreateKlaviyoEventDTO;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;

class KlaviyoService {

    public Client $client;

    public function __construct(){
        $this->client = new Client([
            'base_uri' => config('klaviyo.api_url'),
            'headers' => [
                'Authorization' => 'Klaviyo-API-Key ' . config('klaviyo.private_key'),
                'Accept' => 'application/vnd.api+json',
                'content-type' => 'application/vnd.api+json',
                'revision' => '2025-04-15',
            ]
        ],);
    }

    /**
     * Get the list of email templates
     *
     * @return array
     * @throws \Exception
     */
    public function getTemplates(): array
    {
        try {
            $response = $this->client->get('api/templates');
        } catch (GuzzleException $e) {
            Log::error('Error fetching Klaviyo templates: ' . $e->getMessage());
            throw new \Exception('Error fetching Klaviyo templates: ' . $e->getMessage());
        }
        if ($response->getStatusCode() == 200) {
            $body = json_decode($response->getBody(), true);
            return $body['data'] ?? [];
        } else {
            throw new \Exception('Failed to fetch templates: ' . $response->getStatusCode());
        }
    }

    /**
     * Create a new event in Klaviyo
     *
     * @param CreateKlaviyoEventDTO $data
     * @return array
     * @throws \Exception
     */
    public function createEvent(CreateKlaviyoEventDTO $data): array
    {
        try {
            $response = $this->client->post('api/events', [
                'body' => json_encode($data->toApiPayload()),
            ]);
            if ($response->getStatusCode() == 202) {
                $body = json_decode($response->getBody(), true);
                return $body['data'] ?? [];
            } else {
                throw new \Exception('Failed to create event: ' . $response->getStatusCode());
            }
        } catch (GuzzleException $e) {
            Log::error('Request failed Klaviyo: ' . $e->getMessage());
            throw new \Exception('Request failed Klaviyo: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating Klaviyo event: ' . $e->getMessage());
            throw new \Exception('Error creating Klaviyo event: ' . $e->getMessage());
        }
    }

    /**
     * Get the list of catalog items from Klaviyo
     *
     * @return array
     * @throws \Exception
     */
    public function getCatalogItems(string $fullUrl = null): array
    {
        try {
            $response = $fullUrl
                ? $this->client->get($fullUrl)
                : $this->client->get('api/catalog-items', [
                    'query' => [
                        'fields[catalog-item]' => 'external_id',
                    ],
                ]);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody(), true);
            } else {
                throw new \Exception('Failed to fetch catalog items: ' . $response->getStatusCode());
            }
        } catch (GuzzleException $e) {
            Log::error('Request failed Klaviyo: ' . $e->getMessage());
            throw new \Exception('Request failed Klaviyo: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error fetching catalog items: ' . $e->getMessage());
            throw new \Exception('Error fetching catalog items: ' . $e->getMessage());
        }
    }

    /**
     * Send a bulk update job for catalog items to Klaviyo
     *
     * @param BulkCatalogJobDTO $job
     * @return array
     * @throws \Exception
     */
    public function bulkUpdateCatalogItems(BulkCatalogJobDTO $job): array
    {
        try {
            $response = $this->client->post('api/catalog-item-bulk-update-jobs', [
                'body' => json_encode($job->toApiPayload()),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk update: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk update): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk update): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog update: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog update: ' . $e->getMessage());
        }
    }

    /**
     * Send a bulk create job for catalog items to Klaviyo
     * @param BulkCatalogJobDTO $job
     * @return array
     * @throws \Exception
     */
    public function bulkCreateCatalogItems(BulkCatalogJobDTO $job): array {
        try {
            $response = $this->client->post('api/catalog-item-bulk-create-jobs', [
                'body' => json_encode($job->toApiPayload()),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk create: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk create): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk create): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog create: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog create: ' . $e->getMessage());
        }
    }

    /**
     * Bulk delete catalog items in Klaviyo
     * @param string[] $ids
     * @return array
     * @throws \Exception
     */
    public function bulkDeleteCatalogItems(array $ids): array
    {
        try {
            $response = $this->client->post('api/catalog-item-bulk-delete-jobs', [
                'body' => json_encode([
                    'data' => [
                        'type' => 'catalog-item-bulk-delete-job',
                        'attributes' => [
                            'items' => [
                                'data' => array_map(fn($id) => [
                                    'type' => 'catalog-item',
                                    'id' => $id
                                ], $ids),
                            ],
                        ],
                    ],
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk delete: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk delete): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk delete): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog delete: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog delete: ' . $e->getMessage());
        }
    }

    /**
     * Update a single catalog item in Klaviyo
     *
     * @param int|string $id
     * @param CatalogItemDTO $data
     * @return array
     * @throws \Exception
     */
    public function updateCatalogItem(int|string $id, CatalogItemDTO $data): array
    {
        try {
            $response = $this->client->patch('api/catalog-items/$custom:::$default:::product-' . $id, [
                'body' => json_encode([
                    'data' => $data->toApiItem(true, false),
                ])
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to send bulk update: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (bulk update): ' . $e->getMessage());
            throw new \Exception('Request failed (bulk update): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error in bulk catalog update: ' . $e->getMessage());
            throw new \Exception('Error in bulk catalog update: ' . $e->getMessage());
        }
    }

    /**
     * Create a new catalog item in Klaviyo
     *
     * @param CatalogItemDTO $data
     * @return array
     * @throws \Exception
     */
    public function createCatalogItem(CatalogItemDTO $data): array
    {
        try {
            $response = $this->client->post('api/catalog-items', [
                'body' => json_encode([
                    'data' => $data->toApiItem(false, true),
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to create catalog item: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (create catalog item): ' . $e->getMessage());
            throw new \Exception('Request failed (create catalog item): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating catalog item: ' . $e->getMessage());
            throw new \Exception('Error creating catalog item: ' . $e->getMessage());
        }
    }

    /**
     * Delete a catalog item in Klaviyo
     *
     * @param int $id
     * @return bool
     * @throws \Exception
     */
    public function deleteCatalogItem(int $id): bool
    {
        try {
            $response = $this->client->delete('api/catalog-items/$custom:::$default:::product-' . $id);

            if (in_array($response->getStatusCode(), [200, 204])) {
                return true;
            }

            throw new \Exception('Failed to delete catalog item: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (delete catalog item): ' . $e->getMessage());
            throw new \Exception('Request failed (delete catalog item): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error deleting catalog item: ' . $e->getMessage());
            throw new \Exception('Error deleting catalog item: ' . $e->getMessage());
        }
    }

    /**
     * Get a specific catalog category by ID
     *
     * @param int $id
     * @return array | null
     */
    public function getCatalogCategory(int $id): array | null
    {
        try {
            $response = $this->client->get('api/catalog-categories/$custom:::$default:::category-' . $id);
            Log::debug('Klaviyo getCatalogCategory response: ', [
                'status' => $response->getStatusCode(),
                'body' => $response->getBody()->getContents(),
            ]);
            if ($response->getStatusCode() == 200) {
                return json_decode($response->getBody(), true);
            } else {
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Error fetching catalog category: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new catalog category in Klaviyo
     *
     * @param Category $category
     * @return array
     * @throws \Exception
     */
    public function createCatalogCategory(Category $category): array
    {
        try {
            $response = $this->client->post('api/catalog-categories', [
                'body' => json_encode([
                    'data' => [
                        'type' => 'catalog-category',
                        'attributes' => [
                            'name' => $category->name,
                            'external_id' => 'category-' . $category->id,
                        ],
                    ],
                ]),
            ]);

            if (in_array($response->getStatusCode(), [200, 201, 202])) {
                return json_decode($response->getBody(), true);
            }

            throw new \Exception('Failed to create catalog category: ' . $response->getStatusCode());

        } catch (GuzzleException $e) {
            Log::error('Request failed (create catalog category): ' . $e->getMessage());
            throw new \Exception('Request failed (create catalog category): ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Error creating catalog category: ' . $e->getMessage());
            throw new \Exception('Error creating catalog category: ' . $e->getMessage());
        }
    }

    /**
     * Sync a placed order to Klaviyo (send "Placed Order" event)
     *
     * @param Order $order
     */
    public function syncOrder(Order $order): void
    {
        try {
            if (!config('klaviyo.enabled')) {
                return;
            }

            $products = collect($order->products ?? []);

            $extractCategories = function ($str) {
                if (!$str) return [];
                return collect(explode(',', $str))
                    ->map(fn($s) => trim($s))
                    ->filter()
                    ->values()
                    ->all();
            };

            $categories = $products->map(function ($p) use ($extractCategories) {
                return $extractCategories(data_get($p, 'categories_string'));
            })->flatten()->filter()->unique()->values()->all();

            $itemNames = $products->pluck('title')->filter()->values()->all();
            $brands = $products->pluck('vendor')->filter()->unique()->values()->all();

            $billing = (array) data_get($order, 'payments.creditInfo', []);
            $shipping = (array) data_get($order, 'shipping.shippingInfo', []);

            $splitName = function ($name) {
                $first = null; $last = null;
                if ($name) {
                    $parts = preg_split('/\s+/', trim($name));
                    $first = $parts[0] ?? null;
                    $last = count($parts) > 1 ? implode(' ', array_slice($parts, 1)) : null;
                }
                return [$first, $last];
            };

            [$billFirst, $billLast] = $splitName($billing['name'] ?? null);
            [$shipFirst, $shipLast] = $splitName($shipping['name'] ?? null);

            $items = $products->map(function ($p) use ($extractCategories) {
                $qty = (int) data_get($p, 'quantity', 0);
                $price = (float) data_get($p, 'price', 0);
                $path = data_get($p, 'path');
                $url = $path ? url($path) : null;
                return [
                    'ProductID' => data_get($p, 'product_id') ?? data_get($p, 'id'),
                    'SKU' => data_get($p, 'sku'),
                    'ProductName' => data_get($p, 'title'),
                    'Quantity' => $qty,
                    'ItemPrice' => $price,
                    'RowTotal' => round($price * $qty, 2),
                    'ProductURL' => $url,
                    'ImageURL' => data_get($p, 'media'),
                    'Categories' => $extractCategories(data_get($p, 'categories_string')),
                    'Brand' => data_get($p, 'vendor'),
                ];
            })->values()->all();

            $properties = [
                'OrderId' => $order->id,
                'Categories' => $categories,
                'ItemNames' => $itemNames,
                'DiscountCode' => data_get($order, 'discount.name'),
                'DiscountValue' => (float) ($order->discount_amount ?? 0),
                'Brands' => $brands,
                'time' => optional($order->created_at)->toIso8601String(),
                'value' => (float) $order->grand_total,
                '$value' => (float) $order->grand_total,
                'value_currency' => 'USD',
                'BillingAddress' => [
                    'FirstName' => $billFirst,
                    'LastName' => $billLast,
                    'Address1' => $billing['address_line_1'] ?? null,
                    'City' => $billing['city'] ?? null,
                    'RegionCode' => $billing['state'] ?? null,
                    'CountryCode' => $billing['country'] ?? null,
                    'Zip' => $billing['postal_code'] ?? null,
                    'Phone' => $billing['phone'] ?? ($shipping['phone'] ?? null),
                ],
                'ShippingAddress' => [
                    'FirstName' => $shipFirst,
                    'LastName' => $shipLast,
                    'Address1' => $shipping['address_line_1'] ?? null,
                    'City' => $shipping['city'] ?? null,
                    'RegionCode' => $shipping['state'] ?? null,
                    'CountryCode' => $shipping['country'] ?? null,
                    'Zip' => $shipping['postal_code'] ?? null,
                    'Phone' => $shipping['phone'] ?? null,
                ],
                'Items' => $items,
            ];

            $email = $order->email ?? ($shipping['email'] ?? null);
            if ($email) {
                $event = new CreateKlaviyoEventDTO(
                    metric_name: 'Placed Order',
                    profile_email: $email,
                    properties: $properties,
                );
                $this->createEvent($event);
            }
        } catch (\Throwable $e) {
            Log::error('Error sending Klaviyo order event', [
                'order_id' => $order->id,
                'error' => $e->getMessage(),
            ]);
        }
    }
}
