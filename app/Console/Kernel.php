<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('backup:logs')->daily();
        $schedule->command('backup:run --only-db')->daily();
        $schedule->command('combinations:update')->daily();

        $schedule->command('sitemap:generate')->weekly()->mondays()->at('1:00');

        $schedule->command('sale:apply')->dailyAt('12:00');
        $schedule->command('send-products-to-google')->dailyAt('1:00');

        $schedule->command('subscription:send-emails')->dailyAt('00:30');
        $schedule->command('subscription:create-orders')->dailyAt('2:00');

        // $schedule->command('recurring:send-emails')->dailyAt('12:30');
        $schedule->command('recurring:create-orders')->dailyAt('2:30');

        $schedule->command('inventory:sync')->dailyAt('1:45');

        // $schedule->command('email:sendAbandonedCart')->twiceDaily(9, 13)->withoutOverlapping();
        // $schedule->command('email:sendAbandonedCart')->twiceDaily(17, 21)->withoutOverlapping();

        $schedule->command('returns:void-old-labels')->dailyAt('1:30');
        $schedule->command('inventory:get-sale-price')->dailyAt('1:30');

        $schedule->command('inventory:get')->everyFiveMinutes();
        // $schedule->command('cache:update')->everyFiveMinutes();

        $schedule->command('delete:inventory-track')->dailyAt('12:15');

        $schedule->command('generate:frequently')->weekly()->tuesdays()->at('1:00');

        $schedule
            ->command('heartbeat')
            ->hourly()
            ->pingOnSuccess('https://heartbeat.uptimerobot.com/m786833327-951762992e8bc0938346c6382afbf51f95e929bb');

        $schedule->command('app:sync-products-to-algolia')->dailyAt('6:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
