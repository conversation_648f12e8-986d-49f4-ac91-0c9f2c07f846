<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AuditPersonalizationData extends Command
{
    protected $signature = 'audit:personalization-data {--months=24}';
    protected $description = 'Audit orders for personalization data inconsistencies';

    public function handle()
    {
        $months = $this->option('months');
        $startDate = Carbon::now()->subMonths($months);
        
        $this->info("Auditing personalization data inconsistencies from {$startDate->format('Y-m-d')}...");
        
        // Get orders with products that might have personalization issues
        $orders = DB::table('orders')
            ->where('created_at', '>=', $startDate)
            ->whereRaw('JSON_LENGTH(products) > 0')
            ->select(['id', 'created_at', 'products'])
            ->get();
        
        $affectedOrders = [];
        $totalInconsistentProducts = 0;
        $issueTypes = [
            'missing_personal_key' => 0,
            'empty_personal_data' => 0,
            'null_personal_data' => 0,
            'invalid_personal_structure' => 0
        ];
        
        foreach ($orders as $order) {
            $products = json_decode($order->products, true);
            if (!is_array($products)) continue;
            
            $inconsistentCount = 0;
            $orderIssueTypes = [];
            
            foreach ($products as $index => $product) {
                $issueType = $this->checkPersonalizationIssue($product);
                
                if ($issueType) {
                    $inconsistentCount++;
                    $issueTypes[$issueType]++;
                    $orderIssueTypes[] = [
                        'index' => $index,
                        'issue_type' => $issueType,
                        'title' => $product['title'] ?? 'Unknown',
                        'sku' => $product['sku'] ?? 'Unknown'
                    ];
                }
            }
            
            if ($inconsistentCount > 0) {
                $affectedOrders[] = (object)[
                    'order_id' => $order->id,
                    'created_at' => $order->created_at,
                    'product_count' => count($products),
                    'inconsistent_products' => $inconsistentCount,
                    'issue_details' => $orderIssueTypes
                ];
                $totalInconsistentProducts += $inconsistentCount;
            }
        }
        
        $totalAffectedOrders = count($affectedOrders);
        
        $this->info("Found {$totalAffectedOrders} affected orders with {$totalInconsistentProducts} inconsistent products");
        
        if ($totalAffectedOrders > 0) {
            // Show issue type breakdown
            $this->showIssueTypeBreakdown($issueTypes);
            
            $this->table(
                ['Order ID', 'Created At', 'Total Products', 'Inconsistent Products'],
                array_map(function($row) {
                    return [
                        $row->order_id,
                        $row->created_at,
                        $row->product_count,
                        $row->inconsistent_products
                    ];
                }, array_slice($affectedOrders, 0, 10))
            );
            
            // Show sample issue details
            $this->showSampleIssueDetails($affectedOrders);
            
            if ($totalAffectedOrders > 50) {
                $this->error("🔴 HIGH PRIORITY: Migration script recommended");
            } elseif ($totalAffectedOrders > 10) {
                $this->warn("🟡 MEDIUM PRIORITY: Consider migration script");
            } else {
                $this->info("🟢 LOW PRIORITY: Code fix may be sufficient");
            }
            
            // Show date patterns
            $this->showDatePatterns($affectedOrders);
        }
        
        return 0;
    }
    
    private function checkPersonalizationIssue($product)
    {
        // Must have valid personalization data
        $hasPersonalization = isset($product['personalization']) && !empty($product['personalization']);
        
        // Must support personalization
        // $supportsPersonalization = isset($product['model']['personalizes']) && !empty($product['model']['personalizes']);
        
        if (!$hasPersonalization ) {
            return false;
        }
        
        // Check various personal data issues
        if (!isset($product['personal'])) {
            return 'missing_personal_key';
        }
        
        $personal = $product['personal'];
        
        // Check if personal is null
        if ($personal === null) {
            return 'null_personal_data';
        }
        
        // Check if personal is empty string, empty array, or false
        if (empty($personal)) {
            return 'empty_personal_data';
        }
        
        // Check if personal has the expected structure but with empty/invalid values
        if (is_array($personal)) {
            // Should have 'string' key for display
            if (!isset($personal['string']) || empty($personal['string'])) {
                return 'invalid_personal_structure';
            }
            
            // Should have 'total' for pricing
            if (!isset($personal['total']) || !is_numeric($personal['total'])) {
                return 'invalid_personal_structure';
            }
        }
        
        return false; // No issue found
    }
    
    private function showIssueTypeBreakdown($issueTypes)
    {
        $this->info("\n=== ISSUE TYPE BREAKDOWN ===");
        foreach ($issueTypes as $type => $count) {
            if ($count > 0) {
                $description = $this->getIssueTypeDescription($type);
                $this->line("{$description}: {$count} products");
            }
        }
    }
    
    private function getIssueTypeDescription($type)
    {
        $descriptions = [
            'missing_personal_key' => 'Missing personal key entirely',
            'empty_personal_data' => 'Personal key exists but is empty',
            'null_personal_data' => 'Personal key is null',
            'invalid_personal_structure' => 'Personal key exists but missing required fields (string/total)'
        ];
        
        return $descriptions[$type] ?? $type;
    }
    
    private function showSampleIssueDetails($affectedOrders)
    {
        $this->info("\n=== SAMPLE ISSUE DETAILS ===");
        
        $sampleOrders = array_slice($affectedOrders, 0, 3);
        foreach ($sampleOrders as $order) {
            $this->line("Order #{$order->order_id}:");
            foreach ($order->issue_details as $issue) {
                $this->line("  - Product: {$issue['title']} (SKU: {$issue['sku']}) - Issue: {$this->getIssueTypeDescription($issue['issue_type'])}");
            }
        }
    }
    
    private function showDatePatterns($affectedOrders)
    {
        $this->info("\n=== DATE PATTERNS ===");
        
        $datePatterns = [];
        foreach ($affectedOrders as $order) {
            // dd($order);
            $day = Carbon::parse($order->created_at)->format('Y-m-d');
            $day = $order->order_id;
            $datePatterns[$day] = ($datePatterns[$day] ?? 0) + $order->inconsistent_products;
        }
        
        ksort($datePatterns);
        foreach ($datePatterns as $month => $count) {
            $this->line("$month: $count inconsistent products");
        }
        
        if (!empty($datePatterns)) {
            $firstMonth = array_keys($datePatterns)[0];
            $this->info("Issue first appeared: $firstMonth");
        }
    }
}
