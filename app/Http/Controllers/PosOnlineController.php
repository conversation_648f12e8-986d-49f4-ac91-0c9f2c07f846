<?php

namespace App\Http\Controllers;

use App\PosMessage;
use Bugsnag;
use Exception;
use GuzzleHttp\Client;

class PosOnlineController extends Controller
{
    public static function CreateOrder($order)
    {
        $client = new Client;

        $tax = 0;
        $subTotal = 0;
        $discount = 0;
        $products = collect([]);

        if ($order->status == 'cancelled') {
            return;
        }

        $order_products = collect($order->products)
            ->filter(function ($product) {
                return data_get($product, 'status') != 'returned' && data_get($product, 'status') != 'cancelled';
            })->values();

        collect($order_products)->each(function ($product, $index) use (&$products, &$subTotal, &$discount, &$tax) {
            if ($product['type'] == 'bundle') {
                $bundle = GetFromFrontEnd([$product])->first()->model;
                $percent = ($bundle->fake_price - $bundle->price) / ($bundle->fake_price * 100);

                collect($product['products'])->where('website_deduct', '>', 0)->each(
                    function ($bundle_product) use (
                        &$products,
                        &$subTotal,
                        &$discount,
                        &$tax,
                        $percent,
                        $bundle,
                        $product
                    ) {
                        $tax += $product['tax_amount'] / collect($products)->count();
                        $discount += (($percent / 100) * $bundle_product['price']);
                        $discount = +number_format($discount, 0, '.', '');
                        $subTotal += $bundle_product['website_deduct'] * $bundle_product['price'];

                        $products->push([
                            'Price' => $bundle_product['price'],
                            'ItemName' => $bundle_product['title'],
                            'Tax' => $bundle_product['tax_amount'],
                            'lookupCode' => trim($bundle_product['sku']),
                            'ReferenceNumber' => 'b_' . $bundle_product['id'],
                            'QuantityOrdered' => $bundle_product['website_deduct'],
                            'ExtendedAfterDiscount' => +number_format(
                                ($bundle_product['website_deduct'] * $bundle_product['price']) - $bundle_product['discount_amount'],
                                2,
                                '.',
                                ''
                            ),
                            'CreateItemIfNotExists' => true,
                        ]);
                    }
                );
            } elseif (data_get($product, 'website_deduct') > 0) {
                $tax += $product['tax_amount'];
                $discount += $product['discount_amount'];
                $subTotal += $product['website_deduct'] * $product['price'];


                $products->push([
                    'Price' => $product['price'],
                    'lookupCode' => trim($product['sku']),
                    'ItemName' => trim($product['title']),
                    'Tax' => $product['tax_amount'],
                    'ReferenceNumber' => 'p_' . $product['id'] . data_get($product, 'item_key'),
                    'QuantityOrdered' => $product['website_deduct'],
                    'ExtendedAfterDiscount' => +number_format(
                        ($product['website_deduct'] * $product['price']) - $product['discount_amount'],
                        2,
                        '.',
                        ''
                    ),
                    'CreateItemIfNotExists' => true,
                ]);
            }
        });

        if ($products->isEmpty()) {
            $order->update([
                'meta->pos_online' => 'No Products to send in'
            ]);
            return;
        }

        $customer = $order->customer;
        $address = $customer->address;

        return;
        try {
            $response = $client->post("https://api5.firstchoicepos.com/v1/Orders/Save", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    "Tax" => +number_format($tax, 2, '.', ''),
                    "IsTaxExempt" => false,
                    "Discount" => +number_format($discount, 2, '.', ''),
                    "SubTotal" => +number_format($subTotal, 2, '.', ''),
                    "DistributeTaxes" => false,
                    "OrderNumber" => 'O_' . $order->id,
                    "OrderEntries" => $products,
                    "TaxName" => env('TAX_NAME'),
                    'BillTo' => $order->customer_id,
                    'CustomerInfo' => [
                        'FirstName' => data_get($customer, 'name'),
                        'MobilePhoneNumber' => data_get($address, 'phone'),
                        'Email' => data_get($customer, 'email'),
                        'ShippingAddress' => [
                            'AddressLine1' => data_get($address, 'address_line_1'),
                            'AddressLine2' => data_get($address, 'address_line_2'),
                            'City' => data_get($address, 'City'),
                            'State' => data_get($address, 'State'),
                            'Zip' => data_get($address, 'Zip'),
                            'Country' => data_get($address, 'Country'),
                        ],
                        'IsTaxExempt' => data_get($customer, 'tax_exempt') ? true : false,
                    ],
                    "SiteAlias" => env('SITE_ALIAS'),
                    "WorkflowStateName" => 'OnlineComplete',
                    "TransactionDate" => now()->parse($order->created_at)->toDateTimeString(),
                    "Total" => +number_format(($subTotal + $tax) - $discount, 2, '.', ''),
                ]
            ]);

            $order->update([
                'meta->pos_online' => $response->getBody()->getContents(),
                'meta->pos_online_in' => true
            ]);

            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');
            $order->update([
                'meta->pos_online' => $message,
                'meta->pos_online_error' => forceArray($result)
            ]);

            PosMessage::create([
                'model_type' => 'App\Order',
                'model_id' => $order->id,
                'action' => 'Create Order',
                'message' => $message,
                'status' => 'Unhandled'
            ]);

            return 'failed';
        }
    }


    public static function CreateReturn($return)
    {
        $client = new Client;

        $tax = 0;
        $subTotal = 0;
        $discount = 0;
        $products = collect([]);

        collect($return->products)->where('return_inventory', 'online')->each(
            function ($product, $index) use (&$products, &$subTotal, &$discount, &$tax, $return) {
                if ($product['type'] == 'bundle') {
                    $bundle = GetFromFrontEnd([$product])->first()->model;
                    $percent = ($bundle->fake_price - $bundle->price) / ($bundle->fake_price * 100);

                    collect($product['products'])->each(
                        function ($bundle_product) use (
                            &$products,
                            &$subTotal,
                            &$discount,
                            &$tax,
                            $percent,
                            $bundle,
                            $index,
                            $return
                        ) {
                            $tax += $bundle_product['tax_amount'];
                            $discount += (($percent / 100) * $bundle_product['price']);
                            $discount = +number_format($discount, 0, '.', '');
                            $subTotal += $bundle_product['price'];

                            $products->push([
                                'Price' => $bundle_product['price'],
                                'ItemName' => $bundle_product['title'],
                                'Tax' => -$bundle_product['tax_amount'],
                                'lookupCode' => trim($bundle_product['sku']),
                                'ReferenceNumber' => 'b_' . $bundle_product['id'],
                                'QuantityOrdered' => -1,
                                'ExtendedAfterDiscount' => -+number_format(
                                    ($bundle_product['price']) - $bundle_product['discount_amount'],
                                    2,
                                    '.',
                                    ''
                                ),
                                'CreateItemIfNotExists' => true,
                                'ReturnMaps' => [
                                    [
                                        'Quantity' => 1,
                                        'TransactionNumber' => $return->order->id,
                                        'ItemReferenceNumber' => 'b_' . $bundle_product['id']
                                    ]
                                ]
                            ]);
                        }
                    );
                } else {
                    $tax += $product['tax_amount'];
                    $discount += $product['discount_amount'];
                    $subTotal += $product['price'];


                    $products->push([
                        'Price' => $product['price'],
                        'LookupCode' => trim($product['sku']),
                        'ItemName' => trim($product['title']),
                        'Tax' => -$product['tax_amount'],
                        'ReferenceNumber' => $index . $product['id'],
                        'QuantityOrdered' => -1,
                        'ExtendedAfterDiscount' => -+number_format(
                            ($product['price']) - $product['discount_amount'],
                            2,
                            '.',
                            ''
                        ),
                        'CreateItemIfNotExists' => true,
                        'ReturnMaps' => [
                            [
                                'Quantity' => 1,
                                'TransactionNumber' => 'O_' . $return->order->id,
                                'ItemReferenceNumber' => 'p_' . $product['id'] . data_get($product, 'item_key'),
                            ]
                        ]
                    ]);
                }
            }
        );

        if ($products->isEmpty()) {
            $return->update([
                'meta->pos_online' => 'No Products to send in'
            ]);
            return;
        }

        $customer = $return->customer;
        $address = $customer->address;

        try {
            $response = $client->post("https://api5.firstchoicepos.com/v1/Orders/Save", [
                'headers' => ['Authorization' => 'Basic ' . env('POS')],
                'json' => [
                    "Tax" => -+number_format($tax, 2, '.', ''),
                    "IsTaxExempt" => false,
                    "Discount" => -+number_format($discount, 2, '.', ''),
                    "SubTotal" => -+number_format($subTotal, 2, '.', ''),
                    "DistributeTaxes" => false,
                    "OrderNumber" => 'WR_' . $return->id,
                    "OrderEntries" => $products,
                    "TaxName" => env('TAX_NAME'),
                    'CustomerInfo' => [
                        'FirstName' => data_get($customer, 'name'),
                        'MobilePhoneNumber' => data_get($address, 'phone'),
                        'Email' => data_get($customer, 'email'),
                        'ShippingAddress' => [
                            'AddressLine1' => data_get($address, 'address_line_1'),
                            'AddressLine2' => data_get($address, 'address_line_2'),
                            'City' => data_get($address, 'City'),
                            'State' => data_get($address, 'State'),
                            'Zip' => data_get($address, 'Zip'),
                            'Country' => data_get($address, 'Country'),
                        ],
                        'IsTaxExempt' => data_get($customer, 'tax_exempt') ? true : false,
                    ],
                    "SiteAlias" => env('SITE_ALIAS'),
                    'BillTo' => $return->customer->id,
                    "WorkflowStateName" => 'OnlineComplete',
                    "TransactionDate" => now()->parse($return->created_at)->toDateTimeString(),
                    "Total" => -number_format(($subTotal + $tax) - $discount, 2, '.', ''),
                ]
            ]);

            $return->update([
                'meta->pos_online' => $response->getBody()->getContents()
            ]);

            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
            $result = $ex->getResponse()->getBody()->getContents();
            $message = data_get(forceArray($result), 'Message');
            $return->update([
                'meta->pos_online' => $message,
                'meta->pos_online_error' => forceArray($result)
            ]);

            PosMessage::create([
                'model_type' => 'App\Returns',
                'model_id' => $return->id,
                'action' => 'Create Return',
                'message' => $message,
                'status' => 'Unhandled'
            ]);


            return 'failed';
        }
    }


    public static function CancelOrder($order)
    {
        $client = new Client;

        if (!data_get($order, 'meta.pos_online_in')) {
            $order->update([
                'meta->pos_online_cancelled' => 'Order Wasn\'t sent in',
            ]);
            return;
        }
        $orderId = 'O_' . $order->id;

        try {
            $response = $client->post(
                "https://api5.firstchoicepos.com/v1/Orders/ChangeState?OrderNumber={$orderId}&NewWorkflowStateName=OnlineVoid",
                [
                    'headers' => ['Authorization' => 'Basic ' . env('POS')],
                ]
            );

            $order->update([
                'meta->pos_online_cancelled' => $response->getBody()->getContents(),
            ]);
            return $response->getBody()->getContents();
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);

            $result = $ex->getResponse()->getBody()->getContents();

            $message = data_get(forceArray($result), 'Message');

            $order->update([
                'meta->pos_online_cancelled' => $message,
            ]);


            PosMessage::create([
                'model_type' => 'App\Order',
                'model_id' => $order->id,
                'action' => 'Cancel Order',
                'message' => $message,
                'status' => 'Unhandled'
            ]);
            return 'failed';
        }
    }
}
