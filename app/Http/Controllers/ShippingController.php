<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Api\EmailsController;
use App\Http\Controllers\Api\ShipEngineController;
use App\Order;
use App\Returns;
use App\VariationInfo;
use App\ZipCode;
use Bugsnag;
use Carbon\Carbon;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

//https://help.shipstation.com/hc/en-us/articles/************-Custom-Store-Development-Guide

class ShippingController extends Controller
{
    public function post(Request $request)
    {
        l([
            'SHIP STATION',
            $request['order_number'],
            $request['carrier']
        ]);
        // if ($request['SS-UserName'] != env('SHIP_STATION_USERNAME') || $request['SS-Password'] != env(
        //         'SHIP_STATION_PASSWORD'
        //     )) {
        //     return;
        // }
        $order = Order::findOrFail($request['order_number']);

        if (!data_get($order, 'meta.captured')) {
            $approved = optional($order->payment)->Capture($order);
            $order->update([
                'payment_status' => 'paid',
                'meta->captured' => true
            ]);
        }

        $order->update([
            'shipping->tracking->carrier' => $request['carrier'],
            'shipping->tracking->service' => $request['service'],
            'shipping->tracking->tracking_number' => $request['tracking_number'],
            'status' => $order->isPickup() ? 'ready' : 'shipped',
        ]);

        if (false) {
            //this will send an email to the customer without tracking
            if (data_get($order, 'shipping.tracking.carrier') == 'Other') {
                EmailsController::SendOrderShipped($order);
                $order->giftNotes()->update([
                    'shipped' => true
                ]);
            }
        }

        $this->AddShipmentInfo($order);

        if (data_get($request, 'source') == 'nova') {
            return;
        }

        if (!data_get($order, 'shipping.tracking.tracking_number')) {
            abort(200);
        }
        try {
            $info = (new ShipEngineController)->GetLinkAndArrivalDate($order);
            $order->update([
                'shipping->tracking->link' => $info['link'],
                'shipping->tracking->arrival_date' => $info['arrival_date'],
            ]);
            (new ShipEngineController)->SetUpWebhook($order);
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
        }

        abort(200);
    }

    public function AddShipmentInfo($order)
    {
        try {
            $client = new Client;

            $response = $client->get("https://ssapi.shipstation.com/shipments?orderNumber={$order->id}", [
                'auth' => [
                    env('SHIP_STATION_API_KEY'),
                    env('SHIP_STATION_API_SECRET')
                ]
            ]);

            $result = $response->getBody()->getContents();
            $result = Arr::last(forceArray($result)['shipments']);
            $order->update([
                'shipping->tracking->shipmentCost' => data_get($result, 'shipmentCost'),
                'shipping->tracking->carrier_code' => data_get($result, 'carrierCode'),
                'shipping->tracking->service_code' => data_get($result, 'serviceCode')
            ]);

            if (!data_get($order, 'shipping.tracking.carrier_code')) {
                $codes = (new ShipEngineController)->GetCodesByService(data_get($order, 'shipping.tracking.service'));

                $order->update([
                    'shipping->tracking->carrier_code' => data_get($codes, 'carrier_code'),
                    'shipping->tracking->service_code' => data_get($codes, 'service_code')
                ]);
            }
        } catch (Exception $ex) {
            Bugsnag::notifyException($ex);
        }
    }

    public function get(Request $request)
    {
        // if ($request['SS-UserName'] != env('SHIP_STATION_USERNAME') || $request['SS-Password'] != env('SHIP_STATION_PASSWORD')) {
        //     return;
        // }
        $orders = \App\Order::shippable()
            ->whereDate('updated_at', '>=', now()->parse($request['start_date']))
            ->whereDate('updated_at', '<=', now()->parse($request['end_date']))
            ->where('status', '!=', 'unpaid')
            ->get();
        $productIds = $orders->pluck('products.*.product_id')->flatten();

        $productsWithVendors = \App\Product::query()
            ->with('vendor')
            ->select(['id', 'vendor_id', 'track_inventory', 'title', 'store_quantity', 'website_quantity'])
            ->whereIn('id', $productIds)
            ->get()
            ->keyBy('id');

        $storeCategories = \App\Product::query()
            ->select([ 'id', 'store_category', 'store_sub_category'])
            ->whereIn('id', $productIds)
            ->get()
            ->mapWithKeys(function ($product) {
                return [$product->id => "$product->store_category > $product->store_sub_category"];
            });
        $text = '<?xml version="1.0" encoding="utf-8"?>
            <Orders pages="1">';
        collect($orders)->each(function ($order) use (&$text, $storeCategories, $productsWithVendors) {
            $customer = $order->customer;

            $its_a_gift = !!collect($order->products)->pluck('gift_options')->filter()->count();

            $shipping_zone = data_get(\App\ZipCode::getCode(data_get($order, 'shipping.shippingInfo.postal_code')), 'shippingZone');

            $vendorNames = collect($order->products)
                ->where('type', '!=', 'giftCard')
                ->where('item_type', '!=', 'digital')
                ->map(function ($item) use ($productsWithVendors) {
                    $product = $productsWithVendors->get($item['product_id'] ?? $item['id']);
                    return $product && $product->vendor ? $product->vendor->name : null;
                })
                ->filter()
                ->unique()
                ->implode(', ');

            // Collect out of stock products for InternalNotes
            $outOfStockProducts = collect($order->products)
                ->where('type', '!=', 'giftCard')
                ->where('item_type', '!=', 'digital')
                ->filter(function ($item) use ($productsWithVendors) {
                    $product = $productsWithVendors->get($item['product_id'] ?? $item['id']);
                    if (!$product) return false;

                    // Check if product tracks inventory and is out of stock
                    if ($product->track_inventory) {
                        // Check if product has no inventory (both store and website quantities are 0 or less)
                        return (
                            ($product->store_quantity ?? 0)
                            + ($product->website_quantity ?? 0)
                            // the stock gets deducted when the order is created
                            // so we should add it back
                            + $item['quantity']
                        ) <= 0;
                    }
                    return false;
                })
                ->pluck('sku')
                ->implode(', ');

            $hasOOS = strlen($outOfStockProducts) > 0;

            $s_order = [
                'id' => $order->id,
                'date' => $order->created_at->format('m/d/Y H:m'),
                'status' => $order->status,
                'updated' => $order->updated_at->format('m/d/Y H:m'),
                'shipping_method' => $order->isPickup() ? 'Store Pickup' : data_get($order, 'shipping.shippingType.name'),
                'shipping_zone' => data_get($shipping_zone, 'name'),
                'shipping_zone_id' => data_get($shipping_zone, 'id'),
                'payment_method' => data_get($order, 'payments.creditInfo.payment_type') ?? 'giftCard',
                'arrives' => \Carbon\Carbon::parse(data_get($order, 'shipping.shippingType.estimated_arrival'))->format('m/d/Y H:m') ?? data_get($order, 'shipping.shippingType.estimated_arrival') ?? '',
                'bill_name' => \Illuminate\Support\Str::limit(data_get($order, 'payments.creditInfo.name'), 95),
                'ship_name' => \Illuminate\Support\Str::limit(data_get($order, 'shipping.shippingInfo.name'), 95),

                'address_line_1' => data_get($order, 'shipping.shippingInfo.address_line_1') ? data_get($order, 'shipping.shippingInfo.address_line_1') : settings()->getValue('store_address_line_1'),
                'address_line_2' => data_get($order, 'shipping.shippingInfo.address_line_2') ? data_get($order, 'shipping.shippingInfo.address_line_2') : settings()->getValue('store_address_line_2'),
                'city' => data_get($order, 'shipping.shippingInfo.city') ? data_get($order, 'shipping.shippingInfo.city') : settings()->getValue('store_city'),
                'state' => data_get($order, 'shipping.shippingInfo.state') ? data_get($order, 'shipping.shippingInfo.state') : settings()->getValue('store_state'),
                'postal_code' => data_get($order, 'shipping.shippingInfo.postal_code') ? data_get($order, 'shipping.shippingInfo.postal_code') : settings()->getValue('store_postal_code'),
                'country' => data_get($order, 'shipping.shippingInfo.country') ? data_get($order, 'shipping.shippingInfo.country') : settings()->getValue('store_country'),

                'phone' => data_get($order, 'shipping.pickupInfo.phone') ?? data_get($order, 'shipping.shippingInfo.phone'),

                'shipping_amount' => $order->shipping_amount ?? 0,

                'its_a_gift' => $its_a_gift ? 'true' : 'false',
                'gift_message' => $its_a_gift ? 'This order includes a Gift Option.' : '',
                'vendor_names' => $vendorNames,
                'internal_notes' => ($hasOOS ? $outOfStockProducts . '&#13;&#10;' : "") . $vendorNames,
                'custom_field_3' => $hasOOS ? 'OOS' : ''
            ];
            $text .= " <Order>
                    <OrderID><![CDATA[{$s_order['id']}]]></OrderID>
                    <OrderNumber><![CDATA[{$s_order['id']}]]></OrderNumber>
                    <OrderDate>{$s_order['date']}</OrderDate>
                    <OrderStatus><![CDATA[{$s_order['status']}]]></OrderStatus>
                    <LastModified>{$s_order['updated']}</LastModified>
                    <ShippingMethod><![CDATA[{$s_order['shipping_method']}]]></ShippingMethod>
                    <PaymentMethod><![CDATA[{$s_order['payment_method']}]]></PaymentMethod>
                    <OrderTotal>{$order->grand_total}</OrderTotal>
                    <TaxAmount>{$order->tax_amount}</TaxAmount>
                    <ShippingAmount>{$s_order['shipping_amount']}</ShippingAmount>

                    <Gift>{$s_order['its_a_gift']}</Gift>
                    <GiftMessage>{$s_order['gift_message']}</GiftMessage>

                    <CustomField1><![CDATA[{$s_order['arrives']}]]></CustomField1>
                    <CustomField2><![CDATA[{$s_order['shipping_zone']}]]></CustomField2>
                    <CustomField3><![CDATA[{$s_order['custom_field_3']}]]></CustomField3>
                    <InternalNotes><![CDATA[{$s_order['internal_notes']}]]></InternalNotes>
                    <Customer>
                    <CustomerCode><![CDATA[{$customer->email}]]></CustomerCode>
                    <BillTo>
                    <Name><![CDATA[{$s_order['bill_name']}]]></Name>
                    </BillTo>
                    <ShipTo>
                    <Name><![CDATA[{$s_order['ship_name']}]]></Name>
                    <Address1><![CDATA[{$s_order['address_line_1']}]]></Address1>
                    <Address2><![CDATA[{$s_order['address_line_2']}]]></Address2>
                    <City><![CDATA[{$s_order['city']}]]></City>
                    <State><![CDATA[{$s_order['state']}]]></State>
                    <PostalCode><![CDATA[{$s_order['postal_code']}]]></PostalCode>
                    <Country><![CDATA[{$s_order['country']}]]></Country>
                    <Phone><![CDATA[{$s_order['phone']}]]></Phone>
                    </ShipTo>
                    </Customer>
                    <Items>
                    ";
            collect($order->products)->where('type', '!=', 'giftCard')->where('item_type', '!=', 'digital')->each(function ($item) use (&$text, $storeCategories) {
                $weight = (float)$item['weight'] ?? 0;
                $sku = trim($item['sku']) ?? '';
                $price = +number_format($item['price'], 2, '.', '') ?? 0.00;
                $media = data_get($item, 'large_media');
                $category = data_get($storeCategories, $item['id']);
                $text .= "<Item>
                            <SKU><![CDATA[{$sku}]]></SKU>
                            <Name><![CDATA[{$item['title']}]]></Name>
                            <ImageUrl><![CDATA[{$media}]]></ImageUrl>
                            <Weight>{$weight}</Weight>
                            <WeightUnits>Pounds</WeightUnits>
                            <Quantity>{$item['quantity']}</Quantity>
                            <UnitPrice>{$price}</UnitPrice>";
                // if (array_key_exists('categories_string', $item)) {
                // $categories_string = \Illuminate\Support\Str::limit(data_get($item, 'categories_string'), 95);
                // $text .= "<Location><![CDATA[{$categories_string}]]></Location>";
                $text .= "<Location><![CDATA[{$category}]]></Location>";
                // }
                $text .= "<Options>";
                if (data_get($item, 'type') == 'variation') {
                    $p = \App\VariationInfo::find(data_get($item, 'id'));
                    $variation = implode(' | ', collect(json_decode(data_get($p, 'meta')))->map(function ($item, $key) {
                        return $key . ': ' . $item;
                    })->values()->all());
                    $text .= "
                                <Option>
                                    <Name><![CDATA[Variation]]></Name>
                                    <Value><![CDATA[{$variation}]]></Value>
                                </Option>
                            ";
                }
                if (array_key_exists('gift_options', $item) && data_get($item, 'gift_options')) {
                    $gift_option_name = data_get($item, 'gift_options.name');
                    $text .= "
                                <Option>
                                    <Name><![CDATA[Gift Option]]></Name>
                                    <Value><![CDATA[{$gift_option_name} (Attach Gift Note)]]></Value>
                                </Option>
                            ";
                }
                if (array_key_exists('vendor', $item)) {
                    $vendor = data_get($item, 'vendor');
                    $text .= "
                                <Option>
                                    <Name><![CDATA[Vendor]]></Name>
                                    <Value><![CDATA[{$vendor}]]></Value>
                                </Option>
                            ";
                }
                if (array_key_exists('personal', $item) && data_get($item, 'personal.string')) {
                    $personal = substr(data_get($item, 'personal.string'), 0, 95);
                    $personal .= strlen($personal) > 94 ? '...' : '';
                    $text .= "
                                <Option>
                                    <Name><![CDATA[Personalize]]></Name>
                                    <Value><![CDATA[{$personal}]]></Value>
                                </Option>
                             ";
                }
                $store_deduct = data_get($item, 'store_deduct');
                $website_deduct = data_get($item, 'website_deduct');
                $text .= "
                <Option>
                    <Name><![CDATA[Store]]></Name>
                    <Value><![CDATA[{$store_deduct}]]></Value>
                </Option>
                ";
                $text .= "
                <Option>
                    <Name><![CDATA[Website]]></Name>
                    <Value><![CDATA[{$website_deduct}]]></Value>
                </Option>
                ";
                $text .= "</Options></Item>";
            });
            $text .= "</Items></Order>";
        });
        $text .= " </Orders>";

        return $text;
    }

    public function track(Request $request)
    {
        $tracking_number = $request['data']['tracking_number'];

        $order = Order::where('shipping->tracking->tracking_number', $tracking_number)->first()
            ??
            Returns::where('shipping->tracking->tracking_number', $tracking_number)->first();

        if (!$order) {
            return;
        }
        $status = $request['data']['status_code'];
        $status_type = get_class($order) == 'App\Order' ? 'status' : 'shipping_status';
        if ($status == 'DE') {
            $order->markAsDelivered();
        } elseif ($status == 'IT') {
            $order->update([
                $status_type => 'inTransit',
                'shipping->tracking->arrival_date' => $request['data']['actual_delivery_date'] ?? $request['data']['estimated_delivery_date'] ?? $order->shipping['tracking']['arrival_date'],
                'shipping->tracking->status' => 'In Transit',

            ]);
        } elseif ($status == 'AT') {
            $order->update([
                $status_type => 'deliveryAttempt',
            ]);
        } elseif ($status == 'AC') {
            $order->update([
                $status_type => 'accepted',
            ]);
            if (get_class($order) == 'App\Order') {
                EmailsController::SendOrderShipped($order);
                $order->giftNotes()->update([
                    'shipped' => true
                ]);
            }
        }

        abort(200);
    }
}
