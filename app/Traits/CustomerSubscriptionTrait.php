<?php

namespace App\Traits;

use App\Emails\AddSubscriber;
use App\Http\Controllers\CampaignMonitorController;

trait CustomerSubscriptionTrait
{
    public $subscriptionOptions = [
        'Active' => 'Subscribe',
        'Unsubscribed' => 'Unsubscribe',
        'Deleted' => 'Forget'
    ];

    public function getSubscriptionStatusAttribute()
    {
        // return CampaignMonitorController::subscribedStatus($this->email);
    }

    public function setSubscriptionStatusAttribute($value)
    {
        return;
        if ($this->subscription_status != $value) {
            $task = data_get($this->subscriptionOptions, $value);
            if (!$task) {
                return;
            }
            $this->$task();
        }
    }

    public function Subscribe()
    {
        // CampaignMonitorController::subscribeCustomer($this);
    }

    public function Unsubscribe()
    {
        return;
        (new AddSubscriber)->unsubscribe($this->email);
    }

    public function Forget()
    {
        return;
        (new AddSubscriber)->delete($this->email);
    }
}
