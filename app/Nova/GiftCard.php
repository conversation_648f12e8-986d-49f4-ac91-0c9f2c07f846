<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\Heading;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Textarea;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

class GiftCard extends Resource
{
    use OrderFulfiller;

    public static string $model = \App\GiftCard::class;

    public static $title = 'code';

    public static $search = [
        'id',
        'code',
        'from_name',
        'to_name',
        'to_email',
        'message'
    ];

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Card ending in', 'code')->resolveUsing(function ($value) {
                return substr($value, -4);
            })->sortable()->exceptOnForms(),
            Text::make('Original Value', 'amount')->resolveUsing(function ($value) {
                return '$' . $value;
            })->sortable()->exceptOnForms(),
            Text::make('Balance')->resolveUsing(function ($value) {
                return '$' . $value;
            })->sortable()->exceptOnForms(),
            Text::make('From Name')->sortable()->exceptOnForms(),
            Text::make('To Name')->sortable()->exceptOnForms(),
            Text::make('To Email')->sortable()->onlyOnForms(),
            Textarea::make('Message')->alwaysShow()->sortable()->onlyOnDetail(),
            Heading::make(
                'By changing the email address, we’ll automatically send them a new egift card email with the original gift card number.'
            )->onlyOnForms(),
            HasMany::make('Transactions')
        ];
    }
    function actions(NovaRequest $request)
    {
        return [
            Actions\VoidGiftCard::make()
        ];
    }   
}
